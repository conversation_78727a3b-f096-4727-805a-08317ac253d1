import { Component, ChangeDetectorRef, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DropdownComponent, DropdownOption } from '../../../../../play-comp-library/src/public-api';
import { CascadingDropdownDataService } from './cascading-dropdown-data.service';

@Component({
  selector: 'app-cascading-dropdown',
  standalone: true,
  imports: [CommonModule, DropdownComponent],
  templateUrl: './app-cascading-dropdown.component.html',
  styleUrl: './app-cascading-dropdown.component.scss'
})
export class AppCascadingDropdownComponent implements OnInit {

  constructor(
    private cdr: ChangeDetectorRef,
    private dataService: CascadingDropdownDataService
  ) {}

  ngOnInit() {
    this.loadCategories();
  }

  // First dropdown options - Categories (will be loaded from service)
  categoryOptions: DropdownOption[] = [];

  // Second dropdown options - will be populated based on category selection
  subCategoryOptions: DropdownOption[] = [];

  // Track selections - These values can be used by developers
  selectedCategory: string = '';
  selectedSubCategory: string = '';
  selectedCategoryValue: string = '';
  selectedSubCategoryValue: string = '';

  // Control second dropdown state
  isSecondDropdownDisabled: boolean = true;

  // Complete selection data for API calls or form submissions
  selectionData = {
    category: { name: '', value: '' },
    subCategory: { name: '', value: '' },
    isComplete: false
  };

  // Load categories from service
  private loadCategories() {
    console.log('Component: Loading categories...');
    this.dataService.getCategories().subscribe({
      next: (categories) => {
        console.log('Component: Categories received:', categories);
        this.categoryOptions = categories;
        this.cdr.detectChanges();
      },
      error: (error) => {
        console.error('Component: Error loading categories:', error);
        // Fallback to empty array or show error message
        this.categoryOptions = [];
      }
    });
  }

  // Handle first dropdown (category) selection
  onCategoryChange(selection: any) {
    if (selection && selection.selectedOptions && selection.selectedOptions.length > 0) {
      const selectedOption = selection.selectedOptions[0];

      // Update selection data
      this.selectedCategory = selectedOption.name;
      this.selectedCategoryValue = selectedOption.value;
      this.selectedSubCategory = '';
      this.selectedSubCategoryValue = '';

      // Update selection data object
      this.selectionData = {
        category: { name: selectedOption.name, value: selectedOption.value },
        subCategory: { name: '', value: '' },
        isComplete: false
      };

      this.isSecondDropdownDisabled = true;
      this.loadSubCategoryOptions(selectedOption.value);

      setTimeout(() => {
        this.isSecondDropdownDisabled = false;
        this.cdr.detectChanges();
      }, 100);
    } else {
      this.resetSelections();
    }
  }

  // Handle second dropdown (sub-category) selection
  onSubCategoryChange(selection: any) {
    if (selection && selection.selectedOptions && selection.selectedOptions.length > 0) {
      const selectedOption = selection.selectedOptions[0];

      // Update selection data
      this.selectedSubCategory = selectedOption.name;
      this.selectedSubCategoryValue = selectedOption.value;

      // Update complete selection data object
      this.selectionData = {
        category: { name: this.selectedCategory, value: this.selectedCategoryValue },
        subCategory: { name: selectedOption.name, value: selectedOption.value },
        isComplete: true
      };

      // Here developers can add their logic for API calls, form submissions, etc.
      this.onSelectionComplete();
    } else {
      this.selectedSubCategory = '';
      this.selectedSubCategoryValue = '';
      this.selectionData.subCategory = { name: '', value: '' };
      this.selectionData.isComplete = false;
    }
  }

  // Load sub-category options based on category
  private loadSubCategoryOptions(categoryValue: string) {
    this.dataService.getSubCategories(categoryValue).subscribe({
      next: (subCategories) => {
        this.subCategoryOptions = subCategories.map(cat => ({
          name: cat.name,
          value: cat.value
        }));
        this.cdr.detectChanges();
      },
      error: (error) => {
        console.error('Error loading sub-categories:', error);
        this.subCategoryOptions = [];
        this.cdr.detectChanges();
      }
    });
  }

  // Get dynamic title for second dropdown
  getSecondDropdownTitle(): string {
    if (this.selectedCategory) {
      return `Select ${this.selectedCategory}`;
    }
    return 'Select Sub-category';
  }

  // Reset both dropdowns
  resetSelections() {
    this.selectedCategory = '';
    this.selectedSubCategory = '';
    this.selectedCategoryValue = '';
    this.selectedSubCategoryValue = '';
    this.subCategoryOptions = [];
    this.isSecondDropdownDisabled = true;

    // Reset selection data object
    this.selectionData = {
      category: { name: '', value: '' },
      subCategory: { name: '', value: '' },
      isComplete: false
    };

    this.cdr.detectChanges();
  }

  // Called when both selections are complete - developers can customize this
  onSelectionComplete() {
    // Example: You can make API calls, update forms, navigate, etc.
    // console.log('Selection completed:', this.selectionData);

    // Example API call structure:
    // this.apiService.getFilteredData(this.selectionData.category.value, this.selectionData.subCategory.value);

    // Example form update:
    // this.form.patchValue({ category: this.selectionData.category.value, item: this.selectionData.subCategory.value });
  }

  // Get current selection for external use
  getCurrentSelection() {
    return this.selectionData;
  }

  // Get code example for display
  getCodeExample(): string {
    return `// HTML Template
<div class="dropdown-container">
  <!-- First Dropdown - Category Selection -->
  <ava-dropdown
    dropdownTitle="Select Category"
    [options]="categoryOptions"
    [search]="true"
    (selectionChange)="onCategoryChange($event)">
  </ava-dropdown>

  <!-- Second Dropdown - Filtered based on first selection -->
  <ng-container *ngIf="!isSecondDropdownDisabled; else disabledDropdown">
    <ava-dropdown
      [dropdownTitle]="getSecondDropdownTitle()"
      [options]="subCategoryOptions"
      [disabled]="false"
      [search]="true"
      [selectedValue]="''"
      (selectionChange)="onSubCategoryChange($event)">
    </ava-dropdown>
  </ng-container>

  <ng-template #disabledDropdown>
    <ava-dropdown
      [dropdownTitle]="getSecondDropdownTitle()"
      [options]="[]"
      [disabled]="true"
      [search]="true">
    </ava-dropdown>
  </ng-template>
</div>

// TypeScript Component
export class CascadingDropdownComponent implements OnInit {
  // Data properties
  selectedCategory: string = '';
  selectedSubCategory: string = '';
  selectedCategoryValue: string = '';
  selectedSubCategoryValue: string = '';
  isSecondDropdownDisabled: boolean = true;
  subCategoryOptions: DropdownOption[] = [];

  // Complete selection data for developers
  selectionData = {
    category: { name: '', value: '' },
    subCategory: { name: '', value: '' },
    isComplete: false
  };

  // Category options (loaded from service)
  categoryOptions: DropdownOption[] = [];

  constructor(
    private cdr: ChangeDetectorRef,
    private dataService: CascadingDropdownDataService
  ) {}

  ngOnInit() {
    this.loadCategories();
  }

  // Load categories from service
  private loadCategories() {
    this.dataService.getCategories().subscribe({
      next: (categories) => {
        this.categoryOptions = categories;
        this.cdr.detectChanges();
      },
      error: (error) => {
        console.error('Error loading categories:', error);
        this.categoryOptions = [];
      }
    });
  }

  // Handle category selection
  onCategoryChange(selection: any) {
    if (selection?.selectedOptions?.length > 0) {
      const selectedOption = selection.selectedOptions[0];
      this.selectedCategory = selectedOption.name;
      this.selectedCategoryValue = selectedOption.value;
      this.selectedSubCategory = '';
      this.selectedSubCategoryValue = '';

      this.selectionData = {
        category: { name: selectedOption.name, value: selectedOption.value },
        subCategory: { name: '', value: '' },
        isComplete: false
      };

      this.isSecondDropdownDisabled = true;
      this.loadSubCategoryOptions(selectedOption.value);

      setTimeout(() => {
        this.isSecondDropdownDisabled = false;
      }, 100);
    }
  }

  // Handle sub-category selection
  onSubCategoryChange(selection: any) {
    if (selection?.selectedOptions?.length > 0) {
      const selectedOption = selection.selectedOptions[0];
      this.selectedSubCategory = selectedOption.name;
      this.selectedSubCategoryValue = selectedOption.value;

      this.selectionData = {
        category: { name: this.selectedCategory, value: this.selectedCategoryValue },
        subCategory: { name: selectedOption.name, value: selectedOption.value },
        isComplete: true
      };

      this.onSelectionComplete();
    }
  }

  // Load filtered options from service
  private loadSubCategoryOptions(categoryValue: string) {
    this.dataService.getSubCategories(categoryValue).subscribe({
      next: (subCategories) => {
        this.subCategoryOptions = subCategories.map(cat => ({
          name: cat.name,
          value: cat.value
        }));
        this.cdr.detectChanges();
      },
      error: (error) => {
        console.error('Error loading sub-categories:', error);
        this.subCategoryOptions = [];
        this.cdr.detectChanges();
      }
    });
  }

  // Called when selection is complete
  onSelectionComplete() {
    // Add your custom logic here:
    // - API calls
    // - Form updates
    // - Navigation
    // - Data processing
  }

  // Reset all selections
  resetSelections() {
    this.selectedCategory = '';
    this.selectedSubCategory = '';
    this.selectedCategoryValue = '';
    this.selectedSubCategoryValue = '';
    this.subCategoryOptions = [];
    this.isSecondDropdownDisabled = true;
    this.selectionData = {
      category: { name: '', value: '' },
      subCategory: { name: '', value: '' },
      isComplete: false
    };
  }
}`;
  }
}
