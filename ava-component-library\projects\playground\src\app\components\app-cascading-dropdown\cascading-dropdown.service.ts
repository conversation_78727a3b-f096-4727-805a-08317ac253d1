import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { map, catchError } from 'rxjs/operators';

// Interface for category data structure
export interface CategoryData {
  [key: string]: DropdownOption[];
}

@Injectable({
  providedIn: 'root'
})
export class CascadingDropdownService {
  
  private basePath = './assets/dummy/';
  
  constructor(private http: HttpClient) {}

  /**
   * Get all available categories (Car, Fruits, Vegetables)
   * @returns Observable<DropdownOption[]>
   */
  getCategories(): Observable<DropdownOption[]> {
    return this.http.get<DropdownOption[]>(`${this.basePath}categories.json`).pipe(
      catchError(error => {
        console.error('Error loading categories:', error);
        // Fallback data if JSON file fails to load
        return of([
          { name: 'Car', value: 'car' },
          { name: 'Fruits', value: 'fruits' },
          { name: 'Vegetables', value: 'vegetables' }
        ]);
      })
    );
  }

  /**
   * Get all category data (mapping of categories to their items)
   * @returns Observable<CategoryData>
   */
  getCategoryData(): Observable<CategoryData> {
    return this.http.get<CategoryData>(`${this.basePath}category-data.json`).pipe(
      catchError(error => {
        console.error('Error loading category data:', error);
        // Fallback data if JSON file fails to load
        return of({
          car: [
            { name: 'Toyota', value: 'toyota' },
            { name: 'Honda', value: 'honda' },
            { name: 'Ford', value: 'ford' },
            { name: 'BMW', value: 'bmw' },
            { name: 'Mercedes', value: 'mercedes' }
          ],
          fruits: [
            { name: 'Apple', value: 'apple' },
            { name: 'Banana', value: 'banana' },
            { name: 'Orange', value: 'orange' },
            { name: 'Mango', value: 'mango' },
            { name: 'Grapes', value: 'grapes' },
            { name: 'Strawberry', value: 'strawberry' }
          ],
          vegetables: [
            { name: 'Tomato', value: 'tomato' },
            { name: 'Potato', value: 'potato' },
            { name: 'Onion', value: 'onion' },
            { name: 'Carrot', value: 'carrot' },
            { name: 'Broccoli', value: 'broccoli' },
            { name: 'Spinach', value: 'spinach' }
          ]
        });
      })
    );
  }

  /**
   * Get items for a specific category
   * @param categoryValue - The category value (car, fruits, vegetables)
   * @returns Observable<DropdownOption[]>
   */
  getCategoryItems(categoryValue: string): Observable<DropdownOption[]> {
    return this.getCategoryData().pipe(
      map(data => data[categoryValue] || []),
      catchError(error => {
        console.error(`Error loading items for category ${categoryValue}:`, error);
        return of([]);
      })
    );
  }

  /**
   * Search items within a category
   * @param categoryValue - The category value
   * @param searchTerm - The search term
   * @returns Observable<DropdownOption[]>
   */
  searchCategoryItems(categoryValue: string, searchTerm: string): Observable<DropdownOption[]> {
    return this.getCategoryItems(categoryValue).pipe(
      map(items => items.filter(item => 
        item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.value.toLowerCase().includes(searchTerm.toLowerCase())
      ))
    );
  }

  /**
   * Get item details by category and item value
   * @param categoryValue - The category value
   * @param itemValue - The item value
   * @returns Observable<DropdownOption | null>
   */
  getItemDetails(categoryValue: string, itemValue: string): Observable<DropdownOption | null> {
    return this.getCategoryItems(categoryValue).pipe(
      map(items => items.find(item => item.value === itemValue) || null)
    );
  }

  /**
   * Validate if a category exists
   * @param categoryValue - The category value to validate
   * @returns Observable<boolean>
   */
  validateCategory(categoryValue: string): Observable<boolean> {
    return this.getCategories().pipe(
      map(categories => categories.some(category => category.value === categoryValue))
    );
  }

  /**
   * Validate if an item exists in a category
   * @param categoryValue - The category value
   * @param itemValue - The item value to validate
   * @returns Observable<boolean>
   */
  validateCategoryItem(categoryValue: string, itemValue: string): Observable<boolean> {
    return this.getCategoryItems(categoryValue).pipe(
      map(items => items.some(item => item.value === itemValue))
    );
  }
}
