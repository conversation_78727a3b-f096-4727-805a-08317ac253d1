import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';

export interface DropdownOption {
  id: string;
  name: string;
  value: string;
}

export interface CategoryData {
  id: string;
  name: string;
  value: string;
  models?: DropdownOption[];
  items?: DropdownOption[];
}

export interface DataStructure {
  brands?: CategoryData[];
  categories?: CategoryData[];
}

@Injectable({
  providedIn: 'root'
})
export class CascadingDropdownDataService {

  constructor() { }

  // Get main categories (cars, fruits, vegetables)
  getCategories(): Observable<DropdownOption[]> {
    const categories = [
      { id: 'cars', name: 'Cars', value: 'cars' },
      { id: 'fruits', name: 'Fruits', value: 'fruits' },
      { id: 'vegetables', name: 'Vegetables', value: 'vegetables' }
    ];
    return of(categories);
  }

  // Get subcategories based on main category selection
  getSubCategories(categoryValue: string): Observable<CategoryData[]> {
    switch (categoryValue) {
      case 'cars':
        return of(this.getCarsData().brands || []);
      case 'fruits':
        return of(this.getFruitsData().categories || []);
      case 'vegetables':
        return of(this.getVegetablesData().categories || []);
      default:
        return of([]);
    }
  }

  // Get final items based on subcategory selection
  getFinalItems(categoryValue: string, subCategoryValue: string): Observable<DropdownOption[]> {
    switch (categoryValue) {
      case 'cars':
        const carBrand = this.getCarsData().brands?.find(brand => brand.value === subCategoryValue);
        return of(carBrand?.models || []);
      case 'fruits':
        const fruitCategory = this.getFruitsData().categories?.find(cat => cat.value === subCategoryValue);
        return of(fruitCategory?.items || []);
      case 'vegetables':
        const vegetableCategory = this.getVegetablesData().categories?.find(cat => cat.value === subCategoryValue);
        return of(vegetableCategory?.items || []);
      default:
        return of([]);
    }
  }

  // Private methods to simulate JSON data loading
  private getCarsData(): DataStructure {
    return {
      brands: [
        {
          id: 'toyota',
          name: 'Toyota',
          value: 'toyota',
          models: [
            { id: 'camry', name: 'Camry', value: 'camry' },
            { id: 'corolla', name: 'Corolla', value: 'corolla' },
            { id: 'prius', name: 'Prius', value: 'prius' },
            { id: 'rav4', name: 'RAV4', value: 'rav4' },
            { id: 'highlander', name: 'Highlander', value: 'highlander' }
          ]
        },
        {
          id: 'honda',
          name: 'Honda',
          value: 'honda',
          models: [
            { id: 'civic', name: 'Civic', value: 'civic' },
            { id: 'accord', name: 'Accord', value: 'accord' },
            { id: 'crv', name: 'CR-V', value: 'crv' },
            { id: 'pilot', name: 'Pilot', value: 'pilot' },
            { id: 'fit', name: 'Fit', value: 'fit' }
          ]
        },
        {
          id: 'ford',
          name: 'Ford',
          value: 'ford',
          models: [
            { id: 'f150', name: 'F-150', value: 'f150' },
            { id: 'mustang', name: 'Mustang', value: 'mustang' },
            { id: 'escape', name: 'Escape', value: 'escape' },
            { id: 'explorer', name: 'Explorer', value: 'explorer' },
            { id: 'focus', name: 'Focus', value: 'focus' }
          ]
        },
        {
          id: 'bmw',
          name: 'BMW',
          value: 'bmw',
          models: [
            { id: '3series', name: '3 Series', value: '3series' },
            { id: '5series', name: '5 Series', value: '5series' },
            { id: 'x3', name: 'X3', value: 'x3' },
            { id: 'x5', name: 'X5', value: 'x5' },
            { id: 'i3', name: 'i3', value: 'i3' }
          ]
        },
        {
          id: 'mercedes',
          name: 'Mercedes-Benz',
          value: 'mercedes',
          models: [
            { id: 'cclass', name: 'C-Class', value: 'cclass' },
            { id: 'eclass', name: 'E-Class', value: 'eclass' },
            { id: 'sclass', name: 'S-Class', value: 'sclass' },
            { id: 'glc', name: 'GLC', value: 'glc' },
            { id: 'gle', name: 'GLE', value: 'gle' }
          ]
        }
      ]
    };
  }

  private getFruitsData(): DataStructure {
    return {
      categories: [
        {
          id: 'citrus',
          name: 'Citrus Fruits',
          value: 'citrus',
          items: [
            { id: 'orange', name: 'Orange', value: 'orange' },
            { id: 'lemon', name: 'Lemon', value: 'lemon' },
            { id: 'lime', name: 'Lime', value: 'lime' },
            { id: 'grapefruit', name: 'Grapefruit', value: 'grapefruit' },
            { id: 'tangerine', name: 'Tangerine', value: 'tangerine' }
          ]
        },
        {
          id: 'tropical',
          name: 'Tropical Fruits',
          value: 'tropical',
          items: [
            { id: 'mango', name: 'Mango', value: 'mango' },
            { id: 'pineapple', name: 'Pineapple', value: 'pineapple' },
            { id: 'papaya', name: 'Papaya', value: 'papaya' },
            { id: 'coconut', name: 'Coconut', value: 'coconut' },
            { id: 'passion-fruit', name: 'Passion Fruit', value: 'passion-fruit' }
          ]
        },
        {
          id: 'berries',
          name: 'Berries',
          value: 'berries',
          items: [
            { id: 'strawberry', name: 'Strawberry', value: 'strawberry' },
            { id: 'blueberry', name: 'Blueberry', value: 'blueberry' },
            { id: 'raspberry', name: 'Raspberry', value: 'raspberry' },
            { id: 'blackberry', name: 'Blackberry', value: 'blackberry' },
            { id: 'cranberry', name: 'Cranberry', value: 'cranberry' }
          ]
        },
        {
          id: 'stone-fruits',
          name: 'Stone Fruits',
          value: 'stone-fruits',
          items: [
            { id: 'peach', name: 'Peach', value: 'peach' },
            { id: 'plum', name: 'Plum', value: 'plum' },
            { id: 'apricot', name: 'Apricot', value: 'apricot' },
            { id: 'cherry', name: 'Cherry', value: 'cherry' },
            { id: 'nectarine', name: 'Nectarine', value: 'nectarine' }
          ]
        },
        {
          id: 'common',
          name: 'Common Fruits',
          value: 'common',
          items: [
            { id: 'apple', name: 'Apple', value: 'apple' },
            { id: 'banana', name: 'Banana', value: 'banana' },
            { id: 'grape', name: 'Grape', value: 'grape' },
            { id: 'pear', name: 'Pear', value: 'pear' },
            { id: 'kiwi', name: 'Kiwi', value: 'kiwi' }
          ]
        }
      ]
    };
  }

  private getVegetablesData(): DataStructure {
    return {
      categories: [
        {
          id: 'leafy-greens',
          name: 'Leafy Greens',
          value: 'leafy-greens',
          items: [
            { id: 'spinach', name: 'Spinach', value: 'spinach' },
            { id: 'lettuce', name: 'Lettuce', value: 'lettuce' },
            { id: 'kale', name: 'Kale', value: 'kale' },
            { id: 'arugula', name: 'Arugula', value: 'arugula' },
            { id: 'chard', name: 'Swiss Chard', value: 'chard' }
          ]
        },
        {
          id: 'root-vegetables',
          name: 'Root Vegetables',
          value: 'root-vegetables',
          items: [
            { id: 'carrot', name: 'Carrot', value: 'carrot' },
            { id: 'potato', name: 'Potato', value: 'potato' },
            { id: 'onion', name: 'Onion', value: 'onion' },
            { id: 'beet', name: 'Beet', value: 'beet' },
            { id: 'radish', name: 'Radish', value: 'radish' }
          ]
        },
        {
          id: 'cruciferous',
          name: 'Cruciferous Vegetables',
          value: 'cruciferous',
          items: [
            { id: 'broccoli', name: 'Broccoli', value: 'broccoli' },
            { id: 'cauliflower', name: 'Cauliflower', value: 'cauliflower' },
            { id: 'cabbage', name: 'Cabbage', value: 'cabbage' },
            { id: 'brussels-sprouts', name: 'Brussels Sprouts', value: 'brussels-sprouts' },
            { id: 'bok-choy', name: 'Bok Choy', value: 'bok-choy' }
          ]
        },
        {
          id: 'nightshades',
          name: 'Nightshade Vegetables',
          value: 'nightshades',
          items: [
            { id: 'tomato', name: 'Tomato', value: 'tomato' },
            { id: 'bell-pepper', name: 'Bell Pepper', value: 'bell-pepper' },
            { id: 'eggplant', name: 'Eggplant', value: 'eggplant' },
            { id: 'chili-pepper', name: 'Chili Pepper', value: 'chili-pepper' },
            { id: 'jalapeno', name: 'Jalapeño', value: 'jalapeno' }
          ]
        },
        {
          id: 'squash',
          name: 'Squash & Gourds',
          value: 'squash',
          items: [
            { id: 'zucchini', name: 'Zucchini', value: 'zucchini' },
            { id: 'cucumber', name: 'Cucumber', value: 'cucumber' },
            { id: 'pumpkin', name: 'Pumpkin', value: 'pumpkin' },
            { id: 'butternut-squash', name: 'Butternut Squash', value: 'butternut-squash' },
            { id: 'acorn-squash', name: 'Acorn Squash', value: 'acorn-squash' }
          ]
        }
      ]
    };
  }
}
