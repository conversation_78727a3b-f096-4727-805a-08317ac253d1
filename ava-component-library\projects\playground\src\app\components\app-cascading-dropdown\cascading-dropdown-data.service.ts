import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, map, catchError, of } from 'rxjs';

export interface DropdownOption {
  id: string;
  name: string;
  value: string;
}

export interface CategoryData {
  id: string;
  name: string;
  value: string;
  models?: DropdownOption[];
  items?: DropdownOption[];
}

export interface CarsDataStructure {
  cars: {
    brands: CategoryData[];
  };
}

export interface FruitsDataStructure {
  fruits: {
    categories: CategoryData[];
  };
}

export interface VegetablesDataStructure {
  vegetables: {
    categories: CategoryData[];
  };
}

@Injectable({
  providedIn: 'root'
})
export class CascadingDropdownDataService {
  private basePath = 'assets/data/';

  constructor(private http: HttpClient) { }

  // Get main categories (cars, fruits, vegetables)
  getCategories(): Observable<DropdownOption[]> {
    const url = `${this.basePath}categories.json`;
    console.log('Loading categories from:', url);
    return this.http.get<DropdownOption[]>(url).pipe(
      map(data => {
        console.log('Categories loaded successfully:', data);
        return data;
      }),
      catchError(error => {
        console.error('Error loading categories:', error);
        console.error('Failed URL:', url);
        return of([]);
      })
    );
  }

  // Get subcategories based on main category selection
  getSubCategories(categoryValue: string): Observable<CategoryData[]> {
    switch (categoryValue) {
      case 'cars':
        return this.http.get<CarsDataStructure>(`${this.basePath}cars-data.json`).pipe(
          map(data => data.cars.brands || []),
          catchError(error => {
            console.error('Error loading cars data:', error);
            return of([]);
          })
        );
      case 'fruits':
        return this.http.get<FruitsDataStructure>(`${this.basePath}fruits-data.json`).pipe(
          map(data => data.fruits.categories || []),
          catchError(error => {
            console.error('Error loading fruits data:', error);
            return of([]);
          })
        );
      case 'vegetables':
        return this.http.get<VegetablesDataStructure>(`${this.basePath}vegetables-data.json`).pipe(
          map(data => data.vegetables.categories || []),
          catchError(error => {
            console.error('Error loading vegetables data:', error);
            return of([]);
          })
        );
      default:
        return of([]);
    }
  }

  // Get final items based on subcategory selection
  getFinalItems(categoryValue: string, subCategoryValue: string): Observable<DropdownOption[]> {
    switch (categoryValue) {
      case 'cars':
        return this.http.get<CarsDataStructure>(`${this.basePath}cars-data.json`).pipe(
          map(data => {
            const carBrand = data.cars.brands?.find(brand => brand.value === subCategoryValue);
            return carBrand?.models || [];
          }),
          catchError(error => {
            console.error('Error loading car models:', error);
            return of([]);
          })
        );
      case 'fruits':
        return this.http.get<FruitsDataStructure>(`${this.basePath}fruits-data.json`).pipe(
          map(data => {
            const fruitCategory = data.fruits.categories?.find(cat => cat.value === subCategoryValue);
            return fruitCategory?.items || [];
          }),
          catchError(error => {
            console.error('Error loading fruit items:', error);
            return of([]);
          })
        );
      case 'vegetables':
        return this.http.get<VegetablesDataStructure>(`${this.basePath}vegetables-data.json`).pipe(
          map(data => {
            const vegetableCategory = data.vegetables.categories?.find(cat => cat.value === subCategoryValue);
            return vegetableCategory?.items || [];
          }),
          catchError(error => {
            console.error('Error loading vegetable items:', error);
            return of([]);
          })
        );
      default:
        return of([]);
    }
  }
}
